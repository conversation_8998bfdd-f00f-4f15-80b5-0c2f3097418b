import { createClient } from "@/lib/supabase/server";
import { revalidatePath } from "next/cache";

export const login = async (formData: FormData) => {
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    try {
    const supabase = await createClient();
    
    const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      revalidatePath("/");
    } catch (error) {
        console.log(error);
    }
}

export const logout = async () => {
    const supabase = await createClient();
    await supabase.auth.signOut();
};

export const signup = async (formData: FormData) => {
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const firstName = formData.get("first_name") as string;
    const lastName = formData.get("last_name") as string;

    try {
    const supabase = await createClient();
    const  {error} =await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
          },
        },
      });

      if (error)  throw error;
    } catch (error) {
        
    }
  



      

      
};
